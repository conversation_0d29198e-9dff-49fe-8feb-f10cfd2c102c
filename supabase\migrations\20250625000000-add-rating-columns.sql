-- Add new columns to feedback_ratings table for enhanced rating system
ALTER TABLE public.feedback_ratings 
ADD COLUMN IF NOT EXISTS clarity_rating INTEGER CHECK (clarity_rating >= 1 AND clarity_rating <= 5),
ADD COLUMN IF NOT EXISTS helpfulness_rating INTEGER CHECK (helpfulness_rating >= 1 AND helpfulness_rating <= 5),
ADD COLUMN IF NOT EXISTS model_used TEXT,
ADD COLUMN IF NOT EXISTS generated_text TEXT;

-- Add comment to document the new columns
COMMENT ON COLUMN public.feedback_ratings.clarity_rating IS 'User rating for clarity of the feedback (1-5 scale)';
COMMENT ON COLUMN public.feedback_ratings.helpfulness_rating IS 'User rating for helpfulness of the feedback (1-5 scale)';
COMMENT ON COLUMN public.feedback_ratings.model_used IS 'The LLM model used to generate the feedback';
COMMENT ON COLUMN public.feedback_ratings.generated_text IS 'The actual text generated by the LLM';
